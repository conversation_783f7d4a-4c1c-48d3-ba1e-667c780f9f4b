import { getContext, setContext } from 'svelte';
import type { Tables, Database, Enums } from './database.types';
import type { SupabaseClient } from '@supabase/supabase-js';

// Define a type for organization with role
type OrgWithRole = Tables<'organization'> & { role: Enums<'membership_role'> };

export class CurrentOrg {
	orgId = $state<string | null>(null);
	availableOrgs = $state<OrgWithRole[]>([]);
	currentOrg = $state<OrgWithRole | null>(null);

	constructor(orgId: string | null) {
		this.orgId = orgId;
	}

	setOrgId(orgId: string | null) {
		this.orgId = orgId;
		// Update currentOrg when orgId changes
		if (orgId && this.availableOrgs.length > 0) {
			this.currentOrg = this.availableOrgs.find((o) => o.org_id === orgId) ?? null;
		} else {
			this.currentOrg = null;
		}

		// Update localStorage client-side
		this.updateLocalStorage(orgId);
	}

	private updateLocalStorage(orgId: string | null) {
		if (typeof localStorage !== 'undefined') {
			if (orgId) {
				localStorage.setItem(ORG_STORAGE_KEY, orgId);
			} else {
				localStorage.removeItem(ORG_STORAGE_KEY);
			}
		}
	}

	loadOrgIdFromStorage(): string | null {
		if (typeof localStorage !== 'undefined') {
			return localStorage.getItem(ORG_STORAGE_KEY);
		}
		return null;
	}

	getOrgId() {
		return this.orgId;
	}

	getAvailableOrgs() {
		return this.availableOrgs;
	}

	getCurrentOrg() {
		return this.currentOrg;
	}

	async loadOrgs(supabase: SupabaseClient<Database>, userId: string | undefined) {
		if (!userId) {
			this.availableOrgs = [];
			this.setOrgId(null);
			return;
		}

		// Fetch memberships first, then get the organizations
		const { data: memberships, error: membershipError } = await supabase
			.from('membership')
			.select('entity_id, role')
			.eq('user_id', userId)
			.eq('entity_type', 'organization');

		if (membershipError) {
			console.error('Error fetching memberships:', membershipError);
			this.availableOrgs = [];
			this.setOrgId(null);
			return;
		}

		if (!memberships || memberships.length === 0) {
			console.log('No memberships found');
			this.availableOrgs = [];
			this.setOrgId(null);
			return;
		}

		// Get the organization IDs from the memberships
		const membershipOrgIds = memberships.map((m) => m.entity_id);

		// Fetch the organizations
		const { data, error } = await supabase
			.from('organization')
			.select('*')
			.in('org_id', membershipOrgIds);

		if (error) {
			console.error('Error fetching organizations:', error);
			this.availableOrgs = [];
			this.setOrgId(null);
			return;
		}

		if (!data || data.length === 0) {
			console.log('No organizations found');
			this.availableOrgs = [];
			this.setOrgId(null);
			return;
		}

		// Transform the data to include the role from memberships
		const orgs: OrgWithRole[] = data
			.map((org) => {
				// Find the matching membership for this organization
				const membership = memberships.find((m) => m.entity_id === org.org_id);
				if (!membership) {
					console.error('No membership found for organization:', org.org_id);
					return null;
				}

				// Return the organization with the role
				return {
					...org,
					role: membership.role as Enums<'membership_role'>,
				} as OrgWithRole;
			})
			.filter((org): org is OrgWithRole => org !== null);

		this.availableOrgs = orgs;

		const orgIds = orgs.map((org) => org.org_id);

		// determine active org selection
		if (this.orgId && orgIds.includes(this.orgId)) {
			this.currentOrg = orgs.find((o) => o.org_id === this.orgId) ?? null;
		} else if (orgs.length === 1) {
			const first = orgs[0];
			this.setOrgId(first.org_id);
			this.currentOrg = first;
		} else {
			this.setOrgId(null);
			this.currentOrg = null;
		}
	}
}

const ORG_ID_KEY = Symbol('ORG_ID');

export function setCurrentOrgId(orgId: string | null) {
	return setContext(ORG_ID_KEY, new CurrentOrg(orgId));
}

export function getCurrentOrgId() {
	return getContext<ReturnType<typeof setCurrentOrgId>>(ORG_ID_KEY);
}

export const ORG_STORAGE_KEY = 'activeOrgId';
