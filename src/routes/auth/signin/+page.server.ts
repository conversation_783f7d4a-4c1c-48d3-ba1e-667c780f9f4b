import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { superValidate } from 'sveltekit-superforms';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { signInSchema } from '$lib/schemas/auth';

export const load: PageServerLoad = async ({ url }) => {
	const next = url.searchParams.get('next') ?? undefined;
	const form = await superValidate({ next }, zod(signInSchema), { errors: false });
	return { form, next };
};

export const actions: Actions = {
	default: async ({ request, locals, cookies, url }) => {
		const form = await superValidate(request, zod(signInSchema));
		const next = form.data.next || null;
		if (!form.valid) {
			return fail(400, { form });
		}

		const {
			data: { session },
			error,
		} = await locals.supabase.auth.signInWithPassword({
			email: form.data.email,
			password: form.data.password,
		});

		if (error) {
			// Set a user-friendly error message for authentication failures
			form.errors.password = ['Invalid login credentials'];
			return fail(400, { form });
		}

		// Organization selection will be handled client-side

		const token = url.searchParams.get('invite');
		if (token) {
			// If the user was redirected from the invite page, redirect back to the invite page
			return redirect(303, `/auth/invite/${encodeURIComponent(token)}`);
		}
		if (next) {
			return redirect(303, next);
		}
		// On success, redirect to home (organization selection handled client-side)
		return redirect(303, '/');
	},
};
